<?php
/**
 * Franchise Product Assignment
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Validator.php';

// Require franchise authentication
if (!isLoggedIn('franchise')) {
    Response::redirect('login.php');
}

$currentUser = getCurrentUser();
$franchiseId = getCurrentUserId();
$error = '';
$success = '';

// Get franchise details
$db = Database::getInstance();
$franchiseStmt = $db->prepare("SELECT * FROM franchise WHERE id = ?");
$franchiseStmt->execute([$franchiseId]);
$franchiseDetails = $franchiseStmt->fetch();

// Handle product assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'assign_product') {
    verifyCsrfToken();
    
    $validator = new Validator($_POST);
    $validator->required('user_id', 'User is required')
             ->required('product_id', 'Product is required')
             ->required('pv_side', 'PV side is required')
             ->required('quantity', 'Quantity is required');
    
    if ($validator->passes()) {
        try {
            $userId = sanitizeInput($_POST['user_id']);
            $productId = (int) $_POST['product_id'];
            $pvSide = sanitizeInput($_POST['pv_side']);
            $quantity = (int) $_POST['quantity'];
            $description = sanitizeInput($_POST['description'] ?? '');
            
            // Verify user belongs to this franchise
            $userCheckStmt = $db->prepare("SELECT user_id FROM users WHERE user_id = ? AND franchise_id = ?");
            $userCheckStmt->execute([$userId, $franchiseId]);
            if (!$userCheckStmt->fetch()) {
                throw new Exception("User not found or doesn't belong to your franchise");
            }
            
            // Get product details
            $productStmt = $db->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
            $productStmt->execute([$productId]);
            $product = $productStmt->fetch();
            
            if (!$product) {
                throw new Exception("Product not found or inactive");
            }
            
            // Create product assignment request for admin approval
            $requestStmt = $db->prepare("INSERT INTO product_assignment_requests (franchise_id, user_id, product_id, quantity, pv_side, description) VALUES (?, ?, ?, ?, ?, ?)");
            $result = $requestStmt->execute([
                $franchiseId,
                $userId,
                $productId,
                $quantity,
                $pvSide,
                $description ?: "Product assignment: {$product['name']} (Qty: {$quantity})"
            ]);

            if ($result) {
                $success = "Product assignment request submitted successfully! It will be processed after admin approval.";
            } else {
                throw new Exception("Failed to submit product assignment request");
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    } else {
        $error = $validator->getFirstError();
    }
}

// Get franchise users
$usersStmt = $db->prepare("SELECT user_id, username, full_name, email FROM users WHERE franchise_id = ? AND status = 'active' ORDER BY full_name");
$usersStmt->execute([$franchiseId]);
$users = $usersStmt->fetchAll();

// Get active products
$productsStmt = $db->prepare("SELECT * FROM products WHERE status = 'active' ORDER BY name");
$productsStmt->execute();
$products = $productsStmt->fetchAll();

// Get recent assignment requests
$recentStmt = $db->prepare("
    SELECT par.*, u.full_name, u.user_id as user_code, p.name as product_name, p.price, p.pv_value
    FROM product_assignment_requests par
    JOIN users u ON par.user_id = u.user_id
    LEFT JOIN products p ON par.product_id = p.id
    WHERE par.franchise_id = ?
    ORDER BY par.requested_at DESC
    LIMIT 20
");
$recentStmt->execute([$franchiseId]);
$recentAssignments = $recentStmt->fetchAll();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Assignment - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .product-card {
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <div class="text-center text-white mb-4">
                        <i class="fas fa-store fa-3x mb-2"></i>
                        <h5><?php echo htmlspecialchars($franchiseDetails['full_name']); ?></h5>
                        <small><?php echo htmlspecialchars($franchiseDetails['franchise_code']); ?></small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link text-white" href="users.php">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </a>
                        <a class="nav-link text-white active" href="products.php">
                            <i class="fas fa-box me-2"></i>Product Assignment
                        </a>
                        <a class="nav-link text-white" href="pv-transactions.php">
                            <i class="fas fa-chart-line me-2"></i>PV Transactions
                        </a>
                        <a class="nav-link text-white" href="billing.php">
                            <i class="fas fa-file-invoice-dollar me-2"></i>Billing & Invoices
                        </a>
                        <a class="nav-link text-white" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>Reports
                        </a>
                        <a class="nav-link text-white" href="profile.php">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Product Assignment</h2>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#assignProductModal">
                            <i class="fas fa-plus me-2"></i>Assign Product
                        </button>
                    </div>
                    
                    <!-- Messages -->
                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Available Products -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Available Products</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($products)): ?>
                                        <div class="row">
                                            <?php foreach ($products as $product): ?>
                                                <div class="col-md-4 mb-3">
                                                    <div class="card product-card h-100">
                                                        <div class="card-body">
                                                            <h6 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h6>
                                                            <p class="card-text text-muted small"><?php echo htmlspecialchars($product['description']); ?></p>
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <div>
                                                                    <strong><?php echo formatCurrency($product['price']); ?></strong><br>
                                                                    <small class="text-primary"><?php echo formatPV($product['pv_value']); ?></small>
                                                                </div>
                                                                <button class="btn btn-sm btn-outline-primary" onclick="selectProduct(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['name']); ?>', <?php echo $product['pv_value']; ?>)">
                                                                    Select
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">No products available</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Assignments -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Recent Product Assignment Requests</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($recentAssignments)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>User</th>
                                                        <th>Product</th>
                                                        <th>Quantity</th>
                                                        <th>PV Side</th>
                                                        <th>Status</th>
                                                        <th>Date</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($recentAssignments as $assignment): ?>
                                                        <tr>
                                                            <td>
                                                                <strong><?php echo htmlspecialchars($assignment['full_name']); ?></strong><br>
                                                                <small class="text-muted"><?php echo htmlspecialchars($assignment['user_code']); ?></small>
                                                            </td>
                                                            <td><?php echo htmlspecialchars($assignment['product_name'] ?? 'Unknown Product'); ?></td>
                                                            <td><span class="badge bg-primary"><?php echo $assignment['quantity']; ?></span></td>
                                                            <td>
                                                                <span class="badge bg-<?php echo $assignment['pv_side'] === 'left' ? 'info' : 'warning'; ?>">
                                                                    <?php echo ucfirst($assignment['pv_side']); ?>
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <?php
                                                                $statusClass = '';
                                                                switch ($assignment['status']) {
                                                                    case 'pending': $statusClass = 'warning'; break;
                                                                    case 'approved': $statusClass = 'success'; break;
                                                                    case 'rejected': $statusClass = 'danger'; break;
                                                                }
                                                                ?>
                                                                <span class="badge bg-<?php echo $statusClass; ?>">
                                                                    <?php echo ucfirst($assignment['status']); ?>
                                                                </span>
                                                            </td>
                                                            <td><?php echo date('M d, Y H:i', strtotime($assignment['requested_at'])); ?></td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">No product assignment requests yet</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Assign Product Modal -->
    <div class="modal fade" id="assignProductModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Assign Product to User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" name="action" value="assign_product">
                    
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="user_search" class="form-label">Search User *</label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="user_search" placeholder="Search by name, email, or user ID..." autocomplete="off">
                                <input type="hidden" id="user_id" name="user_id" required>
                                <div id="search_results" class="position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm" style="display: none; z-index: 1000; max-height: 200px; overflow-y: auto;"></div>
                            </div>
                            <div id="selected_user" class="mt-2" style="display: none;">
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-user me-2"></i>
                                    <strong>Selected:</strong> <span id="selected_user_name"></span>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearUserSelection()">
                                        <i class="fas fa-times"></i> Change
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="product_id" class="form-label">Select Product *</label>
                            <select class="form-select" id="product_id" name="product_id" required onchange="updatePVPreview()">
                                <option value="">Choose product...</option>
                                <?php foreach ($products as $product): ?>
                                    <option value="<?php echo $product['id']; ?>" data-pv="<?php echo $product['pv_value']; ?>" data-price="<?php echo $product['price']; ?>">
                                        <?php echo htmlspecialchars($product['name']); ?> - <?php echo formatCurrency($product['price']); ?> (<?php echo formatPV($product['pv_value']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="quantity" class="form-label">Quantity *</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" min="1" value="1" required onchange="updatePVPreview()">
                        </div>
                        
                        <div class="mb-3">
                            <label for="pv_side" class="form-label">PV Side *</label>
                            <select class="form-select" id="pv_side" name="pv_side" required>
                                <option value="">Choose side...</option>
                                <option value="left">Left</option>
                                <option value="right">Right</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Optional description..."></textarea>
                        </div>
                        
                        <div class="alert alert-info" id="pvPreview" style="display: none;">
                            <strong>Total PV:</strong> <span id="totalPV">0</span> PV
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Assign Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let searchTimeout;
        let selectedUser = null;

        function selectProduct(productId, productName, pvValue) {
            document.getElementById('product_id').value = productId;
            updatePVPreview();

            // Show the modal
            var modal = new bootstrap.Modal(document.getElementById('assignProductModal'));
            modal.show();
        }

        function updatePVPreview() {
            const productSelect = document.getElementById('product_id');
            const quantityInput = document.getElementById('quantity');
            const pvPreview = document.getElementById('pvPreview');
            const totalPVSpan = document.getElementById('totalPV');

            if (productSelect.value && quantityInput.value) {
                const selectedOption = productSelect.options[productSelect.selectedIndex];
                const pvValue = parseFloat(selectedOption.dataset.pv);
                const quantity = parseInt(quantityInput.value);
                const totalPV = pvValue * quantity;

                totalPVSpan.textContent = totalPV.toFixed(2);
                pvPreview.style.display = 'block';
            } else {
                pvPreview.style.display = 'none';
            }
        }

        // User search functionality
        document.getElementById('user_search').addEventListener('input', function() {
            const query = this.value.trim();
            const resultsDiv = document.getElementById('search_results');

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                resultsDiv.style.display = 'none';
                return;
            }

            searchTimeout = setTimeout(() => {
                searchUsers(query);
            }, 300);
        });

        function searchUsers(query) {
            const resultsDiv = document.getElementById('search_results');

            fetch(`api/search_users.php?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.users && data.users.length > 0) {
                        let html = '';
                        data.users.forEach(user => {
                            html += `
                                <div class="search-result-item p-2 border-bottom" style="cursor: pointer;" onclick="selectUser('${user.user_id}', '${user.display_name}', '${user.display_info}')">
                                    <div class="fw-bold">${user.display_name}</div>
                                    <small class="text-muted">${user.display_info}</small>
                                </div>
                            `;
                        });
                        resultsDiv.innerHTML = html;
                        resultsDiv.style.display = 'block';
                    } else {
                        resultsDiv.innerHTML = '<div class="p-2 text-muted">No users found</div>';
                        resultsDiv.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Search error:', error);
                    resultsDiv.innerHTML = '<div class="p-2 text-danger">Search failed</div>';
                    resultsDiv.style.display = 'block';
                });
        }

        function selectUser(userId, displayName, displayInfo) {
            selectedUser = { userId, displayName, displayInfo };

            document.getElementById('user_id').value = userId;
            document.getElementById('user_search').value = displayName;
            document.getElementById('selected_user_name').textContent = displayName + ' - ' + displayInfo;
            document.getElementById('selected_user').style.display = 'block';
            document.getElementById('search_results').style.display = 'none';
        }

        function clearUserSelection() {
            selectedUser = null;
            document.getElementById('user_id').value = '';
            document.getElementById('user_search').value = '';
            document.getElementById('selected_user').style.display = 'none';
            document.getElementById('search_results').style.display = 'none';
        }

        // Hide search results when clicking outside
        document.addEventListener('click', function(event) {
            const searchContainer = event.target.closest('.position-relative');
            if (!searchContainer) {
                document.getElementById('search_results').style.display = 'none';
            }
        });

        // Add hover effects for search results
        document.addEventListener('DOMContentLoaded', function() {
            const style = document.createElement('style');
            style.textContent = `
                .search-result-item:hover {
                    background-color: #f8f9fa;
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
