<?php
/**
 * Admin Product Assignment Approval Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Validator.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Initialize classes
$pvSystem = new PVSystem();
$db = Database::getInstance();

// Handle approval/rejection actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    verifyCsrfToken();
    
    $requestId = (int) $_POST['request_id'];
    $action = $_POST['action'];
    $adminNotes = sanitizeInput($_POST['admin_notes'] ?? '');
    
    try {
        // Get request details
        $requestStmt = $db->prepare("
            SELECT par.*, u.full_name as user_name, f.full_name as franchise_name, p.name as product_name, p.pv_value
            FROM product_assignment_requests par
            JOIN users u ON par.user_id = u.user_id
            JOIN franchise f ON par.franchise_id = f.id
            JOIN products p ON par.product_id = p.id
            WHERE par.id = ? AND par.status = 'pending'
        ");
        $requestStmt->execute([$requestId]);
        $request = $requestStmt->fetch();

        if (!$request) {
            throw new Exception("Request not found or already processed");
        }

        if ($action === 'approve') {
            // Calculate total PV
            $totalPV = $request['pv_value'] * $request['quantity'];

            // Add PV transaction (PVSystem handles its own transaction)
            $result = $pvSystem->addPV(
                $request['user_id'],
                $totalPV,
                $request['pv_side'],
                'manual',
                $request['product_id'],
                null,
                $request['description'] . " (Admin Approved)",
                'admin',
                $adminId
            );

            if (!$result) {
                throw new Exception("Failed to add PV transaction");
            }

            // Update request status
            $updateStmt = $db->prepare("UPDATE product_assignment_requests SET status = 'approved', processed_at = NOW(), processed_by = ?, admin_notes = ? WHERE id = ?");
            $updateStmt->execute([$adminId, $adminNotes, $requestId]);

            $success = "Product assignment request approved successfully! {$totalPV} PV added to {$request['pv_side']} side for {$request['user_name']}.";

        } elseif ($action === 'reject') {
            // Update request status
            $updateStmt = $db->prepare("UPDATE product_assignment_requests SET status = 'rejected', processed_at = NOW(), processed_by = ?, admin_notes = ? WHERE id = ?");
            $updateStmt->execute([$adminId, $adminNotes, $requestId]);

            $success = "Product assignment request rejected successfully.";
        }

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'pending';
$franchise = $_GET['franchise'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

// Build query conditions
$whereConditions = [];
$params = [];

if ($status && $status !== 'all') {
    $whereConditions[] = "par.status = ?";
    $params[] = $status;
}

if ($franchise) {
    $whereConditions[] = "f.id = ?";
    $params[] = $franchise;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(par.requested_at) >= ?";
    $params[] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(par.requested_at) <= ?";
    $params[] = $dateTo;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get requests
$requestsStmt = $db->prepare("
    SELECT par.*, 
           u.full_name as user_name, u.user_id as user_code,
           f.full_name as franchise_name, f.franchise_code,
           p.name as product_name, p.price, p.pv_value,
           a.full_name as processed_by_name
    FROM product_assignment_requests par
    JOIN users u ON par.user_id = u.user_id
    JOIN franchise f ON par.franchise_id = f.id
    JOIN products p ON par.product_id = p.id
    LEFT JOIN admin a ON par.processed_by = a.id
    {$whereClause}
    ORDER BY par.requested_at DESC
");
$requestsStmt->execute($params);
$requests = $requestsStmt->fetchAll();

// Get franchises for filter
$franchisesStmt = $db->query("SELECT id, franchise_code, full_name FROM franchise WHERE status = 'active' ORDER BY full_name");
$franchises = $franchisesStmt->fetchAll();

// Get statistics
$statsStmt = $db->query("
    SELECT 
        COUNT(*) as total_requests,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests
    FROM product_assignment_requests
");
$stats = $statsStmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Assignment Approvals - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i><?php echo SITE_NAME; ?> Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-1"></i>Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="franchises.php">
                            <i class="fas fa-store me-1"></i>Franchises
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-box me-1"></i>Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="product-approvals.php">
                            <i class="fas fa-check-circle me-1"></i>Product Approvals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="withdrawals.php">
                            <i class="fas fa-money-bill-wave me-1"></i>Withdrawals
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-check-circle me-2"></i>Product Assignment Approvals</h2>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning me-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['pending_requests']); ?></h4>
                            <small class="text-muted">Pending Requests</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success me-3">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['approved_requests']); ?></h4>
                            <small class="text-muted">Approved</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon danger me-3">
                            <i class="fas fa-times"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['rejected_requests']); ?></h4>
                            <small class="text-muted">Rejected</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon primary me-3">
                            <i class="fas fa-list"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_requests']); ?></h4>
                            <small class="text-muted">Total Requests</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Status</option>
                                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="approved" <?php echo $status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                    <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="franchise" class="form-label">Franchise</label>
                                <select class="form-select" id="franchise" name="franchise">
                                    <option value="">All Franchises</option>
                                    <?php foreach ($franchises as $f): ?>
                                        <option value="<?php echo $f['id']; ?>" <?php echo $franchise == $f['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($f['franchise_code'] . ' - ' . $f['full_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($dateFrom); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($dateTo); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Requests Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Product Assignment Requests</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($requests)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Request ID</th>
                                            <th>Franchise</th>
                                            <th>User</th>
                                            <th>Product</th>
                                            <th>Quantity</th>
                                            <th>PV Side</th>
                                            <th>Total PV</th>
                                            <th>Status</th>
                                            <th>Requested</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($requests as $request): ?>
                                            <tr>
                                                <td><strong>#<?php echo $request['id']; ?></strong></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($request['franchise_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($request['franchise_code']); ?></small>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($request['user_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($request['user_code']); ?></small>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($request['product_name']); ?></strong><br>
                                                    <small class="text-muted">₹<?php echo number_format($request['price'], 2); ?></small>
                                                </td>
                                                <td><span class="badge bg-primary"><?php echo $request['quantity']; ?></span></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $request['pv_side'] === 'left' ? 'info' : 'warning'; ?>">
                                                        <?php echo ucfirst($request['pv_side']); ?>
                                                    </span>
                                                </td>
                                                <td><strong><?php echo formatPV($request['pv_value'] * $request['quantity']); ?></strong></td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    switch ($request['status']) {
                                                        case 'pending': $statusClass = 'warning'; break;
                                                        case 'approved': $statusClass = 'success'; break;
                                                        case 'rejected': $statusClass = 'danger'; break;
                                                    }
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusClass; ?>">
                                                        <?php echo ucfirst($request['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('M d, Y H:i', strtotime($request['requested_at'])); ?></td>
                                                <td>
                                                    <?php if ($request['status'] === 'pending'): ?>
                                                        <button class="btn btn-sm btn-success me-1" onclick="showApprovalModal(<?php echo $request['id']; ?>, 'approve', '<?php echo htmlspecialchars($request['user_name']); ?>', '<?php echo htmlspecialchars($request['product_name']); ?>')">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-danger" onclick="showApprovalModal(<?php echo $request['id']; ?>, 'reject', '<?php echo htmlspecialchars($request['user_name']); ?>', '<?php echo htmlspecialchars($request['product_name']); ?>')">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <small class="text-muted">
                                                            <?php if ($request['processed_by_name']): ?>
                                                                By: <?php echo htmlspecialchars($request['processed_by_name']); ?><br>
                                                            <?php endif; ?>
                                                            <?php echo date('M d, Y H:i', strtotime($request['processed_at'])); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No product assignment requests found</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Approval Modal -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" id="modal_request_id" name="request_id">
                    <input type="hidden" id="modal_action" name="action">

                    <div class="modal-header">
                        <h5 class="modal-title" id="modalTitle">Confirm Action</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="modalContent"></div>
                        <div class="mt-3">
                            <label for="admin_notes" class="form-label">Admin Notes (Optional)</label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" placeholder="Add any notes about this decision..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn" id="modalSubmitBtn">Confirm</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showApprovalModal(requestId, action, userName, productName) {
            document.getElementById('modal_request_id').value = requestId;
            document.getElementById('modal_action').value = action;

            const modal = document.getElementById('approvalModal');
            const title = document.getElementById('modalTitle');
            const content = document.getElementById('modalContent');
            const submitBtn = document.getElementById('modalSubmitBtn');

            if (action === 'approve') {
                title.textContent = 'Approve Product Assignment';
                content.innerHTML = `<div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Approve assignment of "${productName}" to "${userName}"?</strong>
                    <p class="mb-0 mt-2">This will add the PV to the user's account and mark the request as approved.</p>
                </div>`;
                submitBtn.className = 'btn btn-success';
                submitBtn.innerHTML = '<i class="fas fa-check me-1"></i>Approve';
            } else {
                title.textContent = 'Reject Product Assignment';
                content.innerHTML = `<div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Reject assignment of "${productName}" to "${userName}"?</strong>
                    <p class="mb-0 mt-2">This will mark the request as rejected and no PV will be added.</p>
                </div>`;
                submitBtn.className = 'btn btn-danger';
                submitBtn.innerHTML = '<i class="fas fa-times me-1"></i>Reject';
            }

            new bootstrap.Modal(modal).show();
        }
    </script>
</body>
</html>
