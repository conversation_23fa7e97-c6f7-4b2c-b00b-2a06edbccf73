<?php
/**
 * Franchise Profile
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require franchise authentication
if (!isLoggedIn('franchise')) {
    Response::redirect('login.php');
}

$currentUser = getCurrentUser();
$franchiseId = getCurrentUserId();

// Get franchise details
$db = Database::getInstance();
$franchiseStmt = $db->prepare("SELECT * FROM franchise WHERE id = ?");
$franchiseStmt->execute([$franchiseId]);
$franchiseDetails = $franchiseStmt->fetch();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <div class="text-center text-white mb-4">
                        <i class="fas fa-store fa-3x mb-2"></i>
                        <h5><?php echo htmlspecialchars($franchiseDetails['full_name']); ?></h5>
                        <small><?php echo htmlspecialchars($franchiseDetails['franchise_code']); ?></small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link text-white" href="users.php">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </a>
                        <a class="nav-link text-white" href="products.php">
                            <i class="fas fa-box me-2"></i>Product Assignment
                        </a>
                        <a class="nav-link text-white" href="pv-transactions.php">
                            <i class="fas fa-chart-line me-2"></i>PV Transactions
                        </a>
                        <a class="nav-link text-white" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>Reports
                        </a>
                        <a class="nav-link text-white active" href="profile.php">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Franchise Profile</h2>
                    </div>
                    
                    <!-- Profile Information -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Franchise Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Franchise Code</label>
                                            <p class="fw-bold"><?php echo htmlspecialchars($franchiseDetails['franchise_code']); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Full Name</label>
                                            <p class="fw-bold"><?php echo htmlspecialchars($franchiseDetails['full_name']); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Username</label>
                                            <p class="fw-bold"><?php echo htmlspecialchars($franchiseDetails['username']); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Email</label>
                                            <p class="fw-bold"><?php echo htmlspecialchars($franchiseDetails['email']); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Phone</label>
                                            <p class="fw-bold"><?php echo htmlspecialchars($franchiseDetails['phone']); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Commission Rate</label>
                                            <p class="fw-bold"><?php echo $franchiseDetails['commission_rate']; ?>%</p>
                                        </div>
                                        <div class="col-12 mb-3">
                                            <label class="form-label text-muted">Address</label>
                                            <p class="fw-bold"><?php echo htmlspecialchars($franchiseDetails['address']); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Status</label>
                                            <p>
                                                <span class="badge bg-<?php echo $franchiseDetails['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                    <?php echo ucfirst($franchiseDetails['status']); ?>
                                                </span>
                                            </p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Member Since</label>
                                            <p class="fw-bold"><?php echo date('F d, Y', strtotime($franchiseDetails['created_at'])); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Quick Stats</h5>
                                </div>
                                <div class="card-body">
                                    <?php
                                    // Get quick stats
                                    $statsStmt = $db->prepare("
                                        SELECT 
                                            COUNT(*) as total_users,
                                            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users
                                        FROM users WHERE franchise_id = ?
                                    ");
                                    $statsStmt->execute([$franchiseId]);
                                    $stats = $statsStmt->fetch();
                                    
                                    $pvStmt = $db->prepare("
                                        SELECT 
                                            COUNT(*) as total_transactions,
                                            SUM(pv_amount) as total_pv
                                        FROM pv_transactions pt
                                        JOIN users u ON pt.user_id = u.user_id
                                        WHERE u.franchise_id = ? AND pt.created_by_type = 'franchise'
                                    ");
                                    $pvStmt->execute([$franchiseId]);
                                    $pvStats = $pvStmt->fetch();
                                    ?>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>Total Users:</span>
                                            <strong><?php echo $stats['total_users']; ?></strong>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>Active Users:</span>
                                            <strong><?php echo $stats['active_users']; ?></strong>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>PV Transactions:</span>
                                            <strong><?php echo $pvStats['total_transactions']; ?></strong>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>Total PV Assigned:</span>
                                            <strong><?php echo formatPV($pvStats['total_pv'] ?? 0); ?></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="mb-0">Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="users.php" class="btn btn-primary">
                                            <i class="fas fa-users me-2"></i>Manage Users
                                        </a>
                                        <a href="products.php" class="btn btn-success">
                                            <i class="fas fa-box me-2"></i>Assign Products
                                        </a>
                                        <a href="pv-transactions.php" class="btn btn-info">
                                            <i class="fas fa-chart-line me-2"></i>View Transactions
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
