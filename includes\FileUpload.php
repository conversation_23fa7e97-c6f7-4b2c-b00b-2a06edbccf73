<?php
/**
 * File Upload Helper Class
 * MLM Binary Plan System
 */

class FileUpload {
    
    private $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    private $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    private $maxFileSize = 5242880; // 5MB
    private $uploadPath = 'uploads/';
    
    /**
     * Upload product image
     */
    public function uploadProductImage($file, $productCode = null) {
        try {
            // Validate file
            $validation = $this->validateFile($file);
            if (!$validation['success']) {
                return $validation;
            }
            
            // Create upload directory if it doesn't exist
            $uploadDir = $this->uploadPath . 'products/';
            if (!is_dir($uploadDir)) {
                if (!mkdir($uploadDir, 0755, true)) {
                    return ['success' => false, 'message' => 'Failed to create upload directory'];
                }
            }
            
            // Generate unique filename
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = ($productCode ? $productCode . '_' : '') . uniqid() . '.' . $extension;
            $filePath = $uploadDir . $filename;
            
            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filePath)) {
                // Resize image if needed
                $this->resizeImage($filePath, 800, 600);
                
                return [
                    'success' => true,
                    'filename' => $filename,
                    'path' => $filePath,
                    'url' => $this->uploadPath . 'products/' . $filename
                ];
            } else {
                return ['success' => false, 'message' => 'Failed to upload file'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Upload error: ' . $e->getMessage()];
        }
    }
    
    /**
     * Validate uploaded file
     */
    private function validateFile($file) {
        // Check if file was uploaded
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'No file uploaded or upload error'];
        }
        
        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            return ['success' => false, 'message' => 'File size exceeds maximum limit (5MB)'];
        }
        
        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $this->allowedTypes)) {
            return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed'];
        }
        
        // Check file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $this->allowedExtensions)) {
            return ['success' => false, 'message' => 'Invalid file extension'];
        }
        
        return ['success' => true];
    }
    
    /**
     * Resize image to fit within specified dimensions
     */
    private function resizeImage($filePath, $maxWidth, $maxHeight) {
        try {
            $imageInfo = getimagesize($filePath);
            if (!$imageInfo) {
                return false;
            }
            
            $originalWidth = $imageInfo[0];
            $originalHeight = $imageInfo[1];
            $imageType = $imageInfo[2];
            
            // Calculate new dimensions
            $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
            
            // If image is already smaller, don't resize
            if ($ratio >= 1) {
                return true;
            }
            
            $newWidth = round($originalWidth * $ratio);
            $newHeight = round($originalHeight * $ratio);
            
            // Create image resource based on type
            switch ($imageType) {
                case IMAGETYPE_JPEG:
                    $sourceImage = imagecreatefromjpeg($filePath);
                    break;
                case IMAGETYPE_PNG:
                    $sourceImage = imagecreatefrompng($filePath);
                    break;
                case IMAGETYPE_GIF:
                    $sourceImage = imagecreatefromgif($filePath);
                    break;
                case IMAGETYPE_WEBP:
                    $sourceImage = imagecreatefromwebp($filePath);
                    break;
                default:
                    return false;
            }
            
            if (!$sourceImage) {
                return false;
            }
            
            // Create new image
            $newImage = imagecreatetruecolor($newWidth, $newHeight);
            
            // Preserve transparency for PNG and GIF
            if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
                imagealphablending($newImage, false);
                imagesavealpha($newImage, true);
                $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
                imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
            }
            
            // Resize image
            imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
            
            // Save resized image
            switch ($imageType) {
                case IMAGETYPE_JPEG:
                    imagejpeg($newImage, $filePath, 90);
                    break;
                case IMAGETYPE_PNG:
                    imagepng($newImage, $filePath);
                    break;
                case IMAGETYPE_GIF:
                    imagegif($newImage, $filePath);
                    break;
                case IMAGETYPE_WEBP:
                    imagewebp($newImage, $filePath, 90);
                    break;
            }
            
            // Clean up
            imagedestroy($sourceImage);
            imagedestroy($newImage);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Image resize error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete uploaded file
     */
    public function deleteFile($filename, $subfolder = 'products') {
        $filePath = $this->uploadPath . $subfolder . '/' . $filename;
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        return true; // File doesn't exist, consider it deleted
    }
    
    /**
     * Get file URL
     */
    public function getFileUrl($filename, $subfolder = 'products') {
        if (empty($filename)) {
            return null;
        }
        return $this->uploadPath . $subfolder . '/' . $filename;
    }
    
    /**
     * Check if file exists
     */
    public function fileExists($filename, $subfolder = 'products') {
        $filePath = $this->uploadPath . $subfolder . '/' . $filename;
        return file_exists($filePath);
    }
}
?>
